<template>
  <q-page class="landing-page">
    <div class="hero-wrapper">
      <HeroSection />
    </div>
    <div class="scroll-section" data-animation="fade-up">
      <ConnectNetworkGrowSection />
    </div>
    <div class="scroll-section" data-animation="slide-left">
      <FeaturesSection />
    </div>
    <div class="scroll-section" data-animation="fade-up">
      <EcosystemMapSection />
    </div>
    <div class="scroll-section" data-animation="slide-right">
      <OurImpactSection />
    </div>
    <div id="news-section" class="news-section scroll-section" data-animation="fade-up">
      <NewsSection />
    </div>

    <div id="signup-section" class="sign-up-section q-py-md scroll-section" data-animation="slide-up" v-if="!isAuthenticated">
      <div class="container q-mx-auto q-px-md">
        <div class="row">
          <div class="col-1" />
          <div class="col-10">
            <div class="text-h3 text-weight-light q-mb-md text-center" style="color: #0D8A3E">Join Our Early Access Program</div>
            <p class="text-body1 text-center q-mb-md" style="max-width: 800px; margin: 0 auto;">
              Be among the first to access our exclusive innovation ecosystem. Early members receive priority matchmaking with potential partners, mentors, and investors, plus special access to resources and events before our official launch. Shape the future of innovation with us!
            </p>
            <p class="text-caption text-center q-mb-lg" style="max-width: 600px; margin: 0 auto; color: #666; font-size: 0.9rem;">
              Choose a category below - you can join as an <strong>Innovator</strong>, <strong>Investor</strong>, <strong>Mentor</strong>, <strong>Industry Expert</strong>, <strong>Academic Institution</strong>, <strong>Student</strong>, or <strong>Government Organisation</strong>
            </p>
          </div>
          <div class="col-1" />
        </div>
        <EarlyAccessForm />
      </div>
    </div>

    <!-- Alternative section for authenticated users -->
    <div v-if="isAuthenticated" class="authenticated-section q-py-md scroll-section" data-animation="fade-up">
      <div class="container q-mx-auto q-px-md">
        <div class="row">
          <div class="col-1" />
          <div class="col-10">
            <div class="text-h3 text-weight-light q-mb-md text-center" style="color: #0D8A3E">Welcome Back!</div>
            <p class="text-body1 text-center q-mb-lg" style="max-width: 800px; margin: 0 auto;">
              You're already part of our innovation ecosystem. Explore your dashboard to connect with other innovators, discover opportunities, and grow your network.
            </p>
            <div class="text-center">
              <q-btn
                @click="goToDashboard"
                color="primary"
                label="Go to Dashboard"
                size="md"
                rounded
                class="text-weight-bold dashboard-btn animate-bounce cta-button"
                aria-label="Go to your dashboard"
              />
            </div>
          </div>
          <div class="col-1" />
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { defineAsyncComponent, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useScrollAnimations } from '../../composables/useScrollAnimations'

const HeroSection = defineAsyncComponent(() => import('../../components/public/HeroSectionCopy.vue'))
const ConnectNetworkGrowSection = defineAsyncComponent(() => import('../../components/public/ConnectNetworkGrowSection.vue'))
const NewsSection = defineAsyncComponent(() => import('../../components/public/NewsSection.vue'))
const EarlyAccessForm = defineAsyncComponent(() => import('../../components/public/EarlyAccessForm.vue'))
const EcosystemMapSection = defineAsyncComponent(() => import('../../components/public/EcosystemMapSection.vue'))
const OurImpactSection = defineAsyncComponent(() => import('../../components/public/OurImpactSection.vue'))
const FeaturesSection = defineAsyncComponent(() => import('../../components/public/FeaturesSectionModified.vue'))

const router = useRouter()
const authStore = useAuthStore()

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated)

// Initialize scroll animations
const { initializeScrollAnimations } = useScrollAnimations()

const goToDashboard = () => {
  router.push('/dashboard')
}

onMounted(() => {
  // Initialize scroll animations after component is mounted
  initializeScrollAnimations()
})
</script>

<style scoped>
.landing-page {
  background: white;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
}

.hero-wrapper {
  background: white;
  padding: 0;
  margin: 0;
  height: 100vh;
  overflow: hidden;
  width: 100%;
  max-width: 100vw;
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Ensure no scroll bars appear anywhere */
.hero-wrapper::-webkit-scrollbar {
  display: none;
}

.hero-wrapper {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Hide scroll bars globally for this page */
.landing-page::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.landing-page {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.container {
  width: 100%;
  max-width: 1400px;
}

/* Section containers */
.scroll-section {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
}

.sign-up-section {
  background-color: #f5f5f5;
  scroll-margin-top: 60px; /* Reduced space for the fixed header */
  scroll-behavior: smooth;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.authenticated-section {
  background-color: #f8f9fa;
  scroll-margin-top: 60px;
  scroll-behavior: smooth;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

.news-section {
  scroll-margin-top: 60px; /* Reduced space for the fixed header */
  scroll-behavior: smooth;
  padding-top: 20px !important;
  padding-bottom: 20px !important;
}

/* Scroll animation styles - Fixed to prevent blank screens */
.scroll-section {
  opacity: 1; /* Start visible to prevent blank screens */
  transform: translateY(0); /* Start in position */
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Only apply initial animation state after page load */
.scroll-section.scroll-animate-ready {
  opacity: 0;
  transform: translateY(50px);
}

.scroll-section.scroll-animate-ready.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Ensure sections are always visible on mobile */
@media (max-width: 768px) {
  .scroll-section {
    opacity: 1 !important;
    transform: translateY(0) !important;
    transition: none !important;
  }

  .scroll-section.scroll-animate-ready {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }
}

/* Animation variants - only apply when ready */
.scroll-section.scroll-animate-ready[data-animation="fade-up"] {
  transform: translateY(50px);
}

.scroll-section.scroll-animate-ready[data-animation="fade-up"].animate {
  transform: translateY(0);
}

.scroll-section.scroll-animate-ready[data-animation="slide-left"] {
  transform: translateX(-50px);
}

.scroll-section.scroll-animate-ready[data-animation="slide-left"].animate {
  transform: translateX(0);
}

.scroll-section.scroll-animate-ready[data-animation="slide-right"] {
  transform: translateX(50px);
}

.scroll-section.scroll-animate-ready[data-animation="slide-right"].animate {
  transform: translateX(0);
}

.scroll-section.scroll-animate-ready[data-animation="slide-up"] {
  transform: translateY(80px);
}

.scroll-section.scroll-animate-ready[data-animation="slide-up"].animate {
  transform: translateY(0);
}

/* Button styles to match hero section */
.cta-button {
  border-radius: 25px !important;
  min-width: 160px;
  height: 40px;
  font-weight: 600;
}

.dashboard-btn {
  background-color: #0D8A3E !important;
  color: white !important;
  border: none !important;
}

.dashboard-btn:hover {
  background-color: #0B7A36 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.3);
}

.animate-bounce {
  animation: gentle-bounce 2s ease-in-out infinite;
}

@keyframes gentle-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* Rubik font for the entire app */
body {
  font-family: 'Rubik', sans-serif;
  scroll-behavior: smooth;
}

@media (max-width: 599px) {
  .container {
    padding: 0 16px !important;
  }

  .col-1 {
    display: none;
  }

  .col-10 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .sign-up-section {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }

  .news-section {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }

  .q-py-xl {
    padding-top: 15px !important;
    padding-bottom: 15px !important;
  }
}
</style>
