<template>
  <section class="hero-section">
    <!-- Mobile Navigation Drawer - Exact copy from MainLayout -->
    <q-drawer
      v-model="mobileMenuOpen"
      bordered
      :width="250"
      class="mobile-nav-drawer"
      side="left"
      behavior="mobile"
    >
      <q-scroll-area class="fit">
        <q-list padding>
          <q-item-label header class="text-primary text-weight-bold">Navigation</q-item-label>

          <!-- Home Link -->
          <q-item clickable v-ripple @click="navigateTo('/')" exact active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="home" color="primary" />
            </q-item-section>
            <q-item-section>Home</q-item-section>
          </q-item>

          <!-- About Link -->
          <q-item clickable v-ripple @click="navigateTo('/about')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="info" color="primary" />
            </q-item-section>
            <q-item-section>About</q-item-section>
          </q-item>

          <!-- Innovation Community Link -->
          <q-item clickable v-ripple @click="navigateTo('/innovation-community?tab=feed')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="people" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Innovation Community</q-item-label>
              <q-item-label caption>Explore our innovation community</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-badge color="accent" text-color="white" rounded>NEW</q-badge>
            </q-item-section>
          </q-item>

          <!-- Contact Us Link -->
          <q-item clickable v-ripple @click="navigateTo('/contact-us')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="contact_support" color="primary" />
            </q-item-section>
            <q-item-section>Contact Us</q-item-section>
          </q-item>

          <!-- Legal & Support Section -->
          <q-separator class="q-my-md" />
          <q-item-label header class="text-primary text-weight-bold">Legal & Support</q-item-label>

          <!-- Privacy Policy -->
          <q-item clickable v-ripple @click="navigateTo('/legal/privacy-policy')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="privacy_tip" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Privacy Policy</q-item-label>
              <q-item-label caption>Data protection & privacy</q-item-label>
            </q-item-section>
          </q-item>

          <!-- Terms & Conditions -->
          <q-item clickable v-ripple @click="navigateTo('/legal/terms-conditions')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="gavel" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Terms & Conditions</q-item-label>
              <q-item-label caption>Platform usage guidelines</q-item-label>
            </q-item-section>
          </q-item>

          <!-- GDPR & Cookies -->
          <q-item clickable v-ripple @click="navigateTo('/legal/gdpr-compliance')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="security" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>GDPR & Cookies</q-item-label>
              <q-item-label caption>Data compliance & cookies</q-item-label>
            </q-item-section>
          </q-item>

          <!-- FAQ -->
          <q-item clickable v-ripple @click="navigateTo('/legal/faq')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="help" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>FAQ</q-item-label>
              <q-item-label caption>Frequently asked questions</q-item-label>
            </q-item-section>
          </q-item>

          <!-- Cookie Preferences -->
          <q-item clickable v-ripple @click="navigateTo('/legal/cookie-preferences')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="cookie" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Cookie Preferences</q-item-label>
              <q-item-label caption>Manage cookie settings</q-item-label>
            </q-item-section>
          </q-item>

        </q-list>
      </q-scroll-area>
    </q-drawer>

    <div class="hero-container">
      <!-- Top Navigation -->
      <div class="hero-nav">
        <!-- Left Section: Hamburger Menu and Logo -->
        <div class="nav-left">
          <button class="hamburger-menu" @click="toggleMobileMenu">
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
            <span class="hamburger-line"></span>
          </button>

          <div class="logo">
            <img src="@/assets/logo/smile-factory-logo-white.svg" alt="Smile Factory" class="logo-img" />
          </div>
        </div>

        <!-- Right Section: Auth-aware Navigation Buttons -->
        <div class="nav-buttons">
          <template v-if="!isAuthenticated">
            <button class="btn-signin" @click="handleSignIn">SIGN IN</button>
            <button class="btn-join" @click="handleJoinNow">JOIN NOW</button>
          </template>
          <template v-else>
            <button class="btn-dashboard" @click="goToDashboard">DASHBOARD</button>
          </template>
        </div>
      </div>

      <!-- Hero Content -->
      <div class="hero-content" :class="{ 'content-loaded': contentLoaded }">
        <!-- Loading Overlay -->
        <div v-if="!contentLoaded" class="loading-overlay">
          <q-spinner-dots size="40px" color="primary" />
          <p class="loading-text">Loading amazing content...</p>
        </div>

        <!-- Badge -->
        <div class="startup-badge animate-element" :style="{ animationDelay: '0.2s' }">
          <span class="badge-icon animate-pulse">⭐</span>
          25+ Startups Launched
        </div>

        <!-- Main Title -->
        <h1 class="hero-title animate-element" :style="{ animationDelay: '0.4s' }">
          Bringing you a vibrant ecosystem that fosters innovation
        </h1>

        <!-- Auth-aware CTA Buttons -->
        <div class="cta-buttons animate-element" :style="{ animationDelay: '0.6s' }">
          <template v-if="!isAuthenticated">
            <button class="btn-cta primary animate-hover" @click="handleJoinCommunity">
              Join the Community Today
            </button>
          </template>
          <button class="btn-cta secondary animate-hover" @click="handleExploreCommunity">
            Explore Community
          </button>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { triggerSignIn, triggerSignUp } from '@/services/unifiedAuthService'

const router = useRouter()
const authStore = useAuthStore()

// Mobile menu state
const mobileMenuOpen = ref(false)

// Loading state for animations
const contentLoaded = ref(false)

// Check if user is authenticated (same as AppHeader)
const isAuthenticated = computed(() => authStore.isAuthenticated)

const handleSignIn = () => {
  triggerSignIn()
}

const handleJoinNow = () => {
  triggerSignUp()
}

const handleJoinCommunity = () => {
  triggerSignUp()
}

const handleExploreCommunity = () => {
  router.push('/innovation-community')
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const goToDashboard = () => {
  router.push('/dashboard')
}

// Navigation function for drawer items - optimized for performance
function navigateTo(path: string) {
  // Close the drawer immediately
  mobileMenuOpen.value = false
  // Navigate without await for faster response
  router.push(path)
}

const navigateToHome = () => navigateTo('/')
const navigateToCommunity = () => navigateTo('/innovation-community')
const navigateToAbout = () => navigateTo('/about')

// Initialize content loading animation
onMounted(() => {
  // Simulate content loading with a short delay for better UX
  setTimeout(() => {
    contentLoaded.value = true
  }, 800)
})
</script>

<style scoped>
.hero-section {
  width: calc(100% - 32px);
  height: calc(100vh - 32px);
  max-width: calc(100vw - 32px);
  background-image: url('@/assets/hero/landing.jpg');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  border-radius: 24px;
  margin: 16px auto;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  box-sizing: border-box;
}

.hero-container {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 24px;
  display: flex;
  flex-direction: column;
  padding: 24px;
  box-sizing: border-box;
}

.hero-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  position: relative;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-img {
  height: 40px;
  width: auto;
}

.nav-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.btn-signin {
  background: transparent;
  border: none;
  color: #83BA26;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-signin:hover {
  background: rgba(131, 186, 38, 0.1);
}

.btn-join {
  background: #83BA26;
  border: none;
  color: white;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-join:hover {
  background: #6fa01f;
  transform: translateY(-2px);
}

.btn-dashboard {
  background: #83BA26;
  border: none;
  color: white;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-dashboard:hover {
  background: #6fa01f;
  transform: translateY(-2px);
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  flex: 1;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
  transition: opacity 0.5s ease;
}

.hero-content:not(.content-loaded) {
  opacity: 0;
}

.hero-content.content-loaded {
  opacity: 1;
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  z-index: 3;
}

.loading-text {
  color: white;
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

/* Animation Classes */
.animate-element {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

.content-loaded .animate-element {
  animation-play-state: running;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.animate-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.startup-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 8px 16px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 32px;
}

.badge-icon {
  font-size: 16px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.1;
  color: white;
  margin-bottom: 40px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 700px;
}

.cta-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-cta {
  font-size: 18px;
  font-weight: 600;
  padding: 16px 32px;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  min-width: 200px;
}

.btn-cta.primary {
  background: #83BA26;
  color: white;
  box-shadow: 0 4px 16px rgba(131, 186, 38, 0.3);
}

.btn-cta.primary:hover {
  background: #6fa01f;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(131, 186, 38, 0.4);
}

.btn-cta.secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
  box-shadow: 0 4px 16px rgba(255, 255, 255, 0.2);
}

.btn-cta.secondary:hover {
  background: white;
  color: #83BA26;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}

/* Hamburger Menu */
.hamburger-menu {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.hamburger-menu:hover {
  background: rgba(255, 255, 255, 0.1);
}

.hamburger-line {
  width: 24px;
  height: 2px;
  background: white;
  transition: all 0.3s ease;
}

/* Mobile Navigation Drawer */
.mobile-nav-drawer {
  background-color: white;
  box-shadow: 1px 0 10px rgba(0, 0, 0, 0.1);
  color: #333333;
}

.mobile-nav-drawer .q-item {
  min-height: 48px;
  border-radius: 8px;
  margin-bottom: 4px;
  color: #333333;
}

.mobile-nav-drawer .q-item:hover {
  background-color: rgba(13, 138, 62, 0.05);
  color: #0D8A3E;
}

.mobile-nav-drawer .q-item.q-router-link-active {
  background-color: rgba(13, 138, 62, 0.1);
  color: #0D8A3E;
  font-weight: 500;
}

.mobile-nav-drawer .q-item-section {
  color: inherit;
}

.mobile-nav-drawer .q-item-label {
  color: #0D8A3E;
  font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .hero-section {
    margin: 8px auto;
    border-radius: 16px;
    height: calc(100vh - 16px);
    width: calc(100% - 16px);
    max-width: calc(100vw - 16px);
  }

  .hero-container {
    border-radius: 16px;
    padding: 16px;
  }

  .hero-nav {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    margin-bottom: 0;
  }

  .nav-buttons {
    gap: 8px;
  }

  .btn-signin,
  .btn-join,
  .btn-dashboard {
    font-size: 12px;
    padding: 6px 12px;
  }

  .hero-content {
    padding: 10px;
    margin-top: 20px;
  }

  .hero-title {
    font-size: 32px;
    margin-bottom: 32px;
  }

  .cta-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .btn-cta {
    font-size: 16px;
    padding: 14px 28px;
    min-width: 250px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    margin: 4px auto;
    border-radius: 12px;
    height: calc(100vh - 8px);
    width: calc(100% - 8px);
    max-width: calc(100vw - 8px);
  }

  .hero-container {
    border-radius: 12px;
    padding: 12px;
  }

  .hero-title {
    font-size: 28px;
  }

  .startup-badge {
    font-size: 12px;
    padding: 6px 12px;
  }

  .logo-img {
    height: 32px;
  }

  .btn-signin,
  .btn-join,
  .btn-dashboard {
    font-size: 10px;
    padding: 4px 8px;
  }
}
</style>
